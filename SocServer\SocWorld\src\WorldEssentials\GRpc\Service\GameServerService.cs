﻿using Gameserver;
using Google.Protobuf;
using Grpc.Core;
using Grpc.Health.V1;
using SimpleJSON;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld.AsyncTask;
using WizardGames.Soc.SocWorld.AsyncTask.Main;
using WizardGames.Soc.SocWorld.Framework.Network;
using WizardGames.Soc.SocWorld.Framework.Threads;
using WizardGames.Soc.SocWorld.Main;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocWorld.GRpcServer
{
    public class WorldHealthCheckService : Health.HealthBase
    {
        public static readonly SocLogger logger = LogHelper.GetLogger(typeof(WorldHealthCheckService));
        public override Task<HealthCheckResponse> Check(HealthCheckRequest request, ServerCallContext context)
        {
            // TODO: 后续根据需要添加健康检查逻辑
            var status = HealthCheckResponse.Types.ServingStatus.NotServing;
            if ((MainThread.Instance.ServerState & EServerState.ProcessReadyAndSimulatorConnected) == EServerState.ProcessReadyAndSimulatorConnected)
            {
                status = HealthCheckResponse.Types.ServingStatus.Serving;
            }
            return Task.FromResult(new HealthCheckResponse
            {
                Status = status
            });
        }
    }

    public class GateHealthCheckService : Health.HealthBase
    {
        public static readonly SocLogger logger = LogHelper.GetLogger(typeof(GateHealthCheckService));
        public override Task<HealthCheckResponse> Check(HealthCheckRequest request, ServerCallContext context)
        {
            var status = HealthCheckResponse.Types.ServingStatus.NotServing;
            if ((MainThread.Instance.ServerState & EServerState.ProcessReadyAndSimulatorConnected) == EServerState.ProcessReadyAndSimulatorConnected)
            {
                status = HealthCheckResponse.Types.ServingStatus.Serving;
            }
            return Task.FromResult(new HealthCheckResponse
            {
                Status = status
            });
        }
    }


    public class GameServerService : GameServer.GameServerBase
    {
        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(GameServerService));

        public ConcurrentDictionary<long, TaskCompletionSource<GMOperateRsp>> DoingGmTasks = new();
        public ConcurrentDictionary<long, TaskCompletionSource<CreateTeamRsp>> DoingCreateTeamTasks = new();
        public ConcurrentDictionary<long, TaskCompletionSource<ReplyTeamInvitationRsp>> DoingReplyTeamInvitationTasks = new();
        public ConcurrentDictionary<long, TaskCompletionSource<StartDynamicBattleRsp>> DoingStartDynamicBattleTasks = new();
        public ConcurrentDictionary<long, TaskCompletionSource<Empty>> DoingKickOutTasks = new();
        public ConcurrentDictionary<long, TaskCompletionSource<ApplyJoinBattleRecruitmentRsp>> DoingJoinBattleRecruitmentTasks = new();
        public ConcurrentDictionary<long, TaskCompletionSource<Empty>> DoingJoinDynamicBattleTasks = new();
        public ConcurrentDictionary<long, TaskCompletionSource<Empty>> DoingStartWarzoneTasks = new();
        public ConcurrentDictionary<long, TaskCompletionSource<Empty>> DoingPushChangeTalentsTasks = new();
        private static long currentSeq;
        // 服务器还没启动好，大厅分配玩家进服缓存
        public ConcurrentDictionary<ulong, object> CreateTeamRoleIds = new();

        public override Task<Empty> StartWarzoneBattle(StartWarzoneBattleReq req, ServerCallContext context)
        {
            logger.Info($"StartWarzoneBattle {req.GameMode} {req.MapID} {req.TeamSize} {req.Platform}");
            var seq = currentSeq++;
            var taskRet = new TaskCompletionSource<Empty>();
            if (!DoingStartWarzoneTasks.TryAdd(seq, taskRet))
            {
                logger.Error($"StartUp {seq} add failed");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Aborted, "task seq duplicated")));
            }

            ThreadHelper.PostToMainThread(new SimpleMainTask(() =>
            {
                MainThread.Instance.TimerWheel.AddTimerOnce(5000, (_, __, ___) =>
                {
                    if (DoingStartWarzoneTasks.TryRemove(seq, out var tcs))
                    {
                        tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "StartWarzoneBattle time out.")));
                    }
                });
            }));

            ThreadHelper.PostToLogicThread(() =>
            {
                ServerInstanceEntity.Instance.LobbyAllocateServerInfo(seq, req);
            });
            return taskRet.Task;
        }

        public void OnStartWarzoneResponse(long seq, EOpCode code)
        {
            if (DoingStartWarzoneTasks.TryRemove(seq, out var tcs))
            {
                logger.Info($"OnStartWarzoneResponse {seq}");
                if (code == EOpCode.Success)
                {
                    tcs.SetResult(new Empty());
                }
                else
                {
                    tcs.SetException(new RpcException(new Status(StatusCode.Aborted, $"errorCode:{code}")));
                }
            }
            else
            {
                logger.Warn($"OnStartWarzoneResponse {seq}, but request not found");
            }
        }

        public override Task<Empty> KickOut(KickReq request, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"KickOut from lobby but server not ready {request.RoleID}");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"KickOut from lobby {request.RoleID}");
            var seq = currentSeq++;
            var taskRet = new TaskCompletionSource<Empty>();
            if (!DoingKickOutTasks.TryAdd(seq, taskRet))
            {
                logger.Error($"KickOut {seq} add failed");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Aborted, "task seq duplicated")));
            }
            ThreadHelper.PostToMainThread(new SimpleMainTask(() =>
            {
                MainThread.Instance.TimerWheel.AddTimerOnce(3000, (_, __, ___) =>
                {
                    if (DoingKickOutTasks.TryRemove(seq, out var tcs))
                    {
                        tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "kick out time out.")));
                    }
                });
            }));
            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.PROCESSS_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.PROCESS_ENTITY_KICK_OUT, ERpcTarget.World);
            packet.AppendParam(seq);
            packet.AppendParam(request.RoleID);
            WorldNetworkHelper.GateCallWorld(packet);
            return taskRet.Task;
        }

        private static JSONObject Timeout()
        {
            var ret = new JSONObject();
            ret.Add("Result", -1);
            ret.Add("RetMsg", $"cmd time out");
            return ret;
        }

        public override Task<GMOperateRsp> GMOperate(GMOperateReq request, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"GMOperate from lobby but server not ready {request.CmdIDX} {request.Param}");
                return Task.FromException<GMOperateRsp>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"GMOperate {request.CmdIDX} {request.Param}");
            if (DoingGmTasks.ContainsKey(request.CmdIDX))
            {
                JSONObject ret = new();
                ret.Add("result", -1);
                ret.Add("retmsg", "duplicate request");
                logger.Warn($"Duplicate operate {request.CmdIDX}");
                return Task.FromResult(new GMOperateRsp { Response = ret.ToString() });
            }
            var seq = currentSeq++;
            var taskSrc = new TaskCompletionSource<GMOperateRsp>();
            DoingGmTasks.TryAdd(seq, taskSrc);
            ThreadHelper.PostToMainThread(new SimpleMainTask(() =>
            {
                MainThread.Instance.TimerWheel.AddTimerOnce(5000, (_, __, ___) =>
                {
                    if (DoingGmTasks.TryRemove(seq, out var tcs))
                    {
                        tcs.SetResult(new GMOperateRsp { Response = Timeout() });
                    }
                });
            }));


            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_ON_GRPC_COMMAND, ERpcTarget.World);
            packet.AppendParam(seq).AppendParam(request.CmdIDX).AppendParam(request.Param);
            WorldNetworkHelper.GateCallWorld(packet);
            return taskSrc.Task;
        }

        public void OnGmOperateResponse(long seq, string response)
        {
            if (DoingGmTasks.TryRemove(seq, out var tcs))
            {
                logger.Info($"OnGmOperateResponse {seq} {response}");
                tcs.SetResult(new GMOperateRsp { Response = response });
            }
            else
            {
                logger.Warn($"OnGmOperateResponse {seq} {response}, but request not found");
            }
        }

        public override Task<CreateTeamRsp> CreateTeam(CreateTeamReq request, ServerCallContext context)
        {
            if ((MainThread.Instance.ServerState & EServerState.ProcessReadyAndSimulatorConnected) != EServerState.ProcessReadyAndSimulatorConnected)
            {
                logger.Warn($"CreateTeam from lobby but server not ready {request.LeaderID}");
                return Task.FromException<CreateTeamRsp>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            if (MainThread.Instance.IsAllReady)
            {
                logger.Info($"CreateTeam logic already ready {request.LeaderID} {request.MemberList.ToString()}");
                var seq = currentSeq++;
                var taskSrc = new TaskCompletionSource<CreateTeamRsp>();
                DoingCreateTeamTasks.TryAdd(seq, taskSrc);
                var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_CREATE_TEAM, ERpcTarget.World);
                packet.AppendParam(seq).AppendParam(request.ToByteArray());
                WorldNetworkHelper.GateCallWorld(packet);
                return taskSrc.Task;
            }
            else
            {
                logger.Info($"CreateTeam logic not ready {request.LeaderID} {request.MemberList.ToString()}");
                var rsp = new CreateTeamRsp()
                {
                    Success = true,
                };

                // 如果有玩家已经分配进这个服务器 直接返回报错
                foreach (var member in request.MemberList)
                {
                    if (CreateTeamRoleIds.ContainsKey(member.RoleID))
                    {
                        logger.Info($"CreateTeam but roleId already in createTeamRoleIds {member.RoleID}");
                        rsp.Success = false;
                        return Task.FromResult(rsp);
                    }
                    else
                    {
                        CreateTeamRoleIds.TryAdd(member.RoleID, null);
                        logger.Info($"CreateTeam add roleId to CreateTeamRoleIds {member.RoleID}");
                    }
                }

                long teamId = 0;
                // 服务器还没准备好 但是大厅要求立刻返回，先返回，后续处理这个请求
                if (request.MemberList.Count > 1)
                {
                    teamId = IdGenerator.GenWarZoneUniqueId();
                }

                rsp.Team = new BattleTeam() { LeaderID = request.LeaderID, TeamID = teamId, BattleServerID = ProcessEntity.Instance.ServerId };
                ThreadHelper.PostToGrpcThread(new CreateTeamTask(teamId, request));
                return Task.FromResult(rsp);
            }
        }

        public override Task<StartDynamicBattleRsp> StartDynamicBattle(StartDynamicBattleReq request, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"StartDynamicBattle from lobby but server not ready {request.BattleID} {request.GameSetting} {request.Players.ToString()}");
                return Task.FromException<StartDynamicBattleRsp>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"StartDynamicBattle {request.BattleID} {request.GameSetting} {request.Players.ToString()}");
            var taskSrc = new TaskCompletionSource<StartDynamicBattleRsp>();

            if (ServerCommandLine.Config.Agones == 0)
            {
                logger.Error("StartDynamicBattle request not support, cur server not room server");
                taskSrc.SetException(new RpcException(new Status(StatusCode.Aborted, "not room server.")));
                return taskSrc.Task;
            }

            var seq = currentSeq++;
            DoingStartDynamicBattleTasks.TryAdd(seq, taskSrc);

            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_START_DYNAMIC_BATTLE, ERpcTarget.World);
            packet.AppendParam(seq).AppendParam(request.ToByteArray());
            WorldNetworkHelper.GateCallWorld(packet);

            // 设置World返回结果超时定时器
            ThreadHelper.PostToMainThread(new SimpleMainTask(() =>
            {
                MainThread.Instance.TimerWheel.AddTimerOnce(10000, (_, __, ___) =>
                {
                    if (DoingStartDynamicBattleTasks.TryRemove(seq, out var tcs))
                    {
                        tcs.SetException(new RpcException(new Status(StatusCode.Cancelled, "time out.")));
                        //通知world服务器 shutdown

                        var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_FORCE_SHUTDOWN_DYNAMIC_BATTLE, ERpcTarget.World);
                        packet.AppendParam(seq);
                        WorldNetworkHelper.GateCallWorld(packet);
                    }
                });
            }));

            return taskSrc.Task;
        }

        public void OnStartDynamicBattleResponse(long seq, int code, byte[] rspBytes)
        {
            var rsp = StartDynamicBattleRsp.Parser.ParseFrom(rspBytes);
            OnStartDynamicBattleResponse(seq, code, rsp);
        }

        public void OnStartDynamicBattleResponse(long seq, int code, StartDynamicBattleRsp rsp)
        {
            if (DoingStartDynamicBattleTasks.TryRemove(seq, out var tcs))
            {
                if (code != 0)
                {
                    // 如果world进程初始化失败，会进行shutdown逻辑，无需通知
                    logger.Warn($"OnStartDynamicBattleResponse {seq}, server init failed, code:{code}");
                    tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "server init failed")));
                }
                else
                {
                    logger.Info($"OnStartDynamicBattleResponse: {seq} {rsp.Token}");
                    tcs.SetResult(rsp);
                }
            }
            else
            {
                // 超时返回，超时定时器逻辑生效，已经通知world进行shutdown操作
                logger.Warn($"OnStartDynamicBattleResponse {seq}, but request not found");
            }
        }

        public override Task<Empty> JoinDynamicBattle(JoinDynamicBattleReq request, ServerCallContext context)
        {
            var taskSrc = new TaskCompletionSource<Empty>();
            if (ServerCommandLine.Config.Agones == 0)
            {
                logger.Error("JoinDynamicBattle request not support, cur server not room server");
                taskSrc.SetException(new RpcException(new Status(StatusCode.Aborted, "not room server.")));
                return taskSrc.Task;
            }
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"JoinDynamicBattle but service not all ready. {request}");
                taskSrc.SetException(new RpcException(new Status(StatusCode.Aborted, "not room server.")));
                return taskSrc.Task;
            }

            logger.Info($"JoinDynamicBattle {request.SupplyID} {request.Players}");

            var seq = currentSeq++;
            DoingJoinDynamicBattleTasks.TryAdd(seq, taskSrc);

            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_JOIN_DYNAMIC_BATTLE, ERpcTarget.World);
            packet.AppendParam(seq).AppendParam(request.ToByteArray());
            WorldNetworkHelper.GateCallWorld(packet);

            // 设置World返回结果超时定时器
            ThreadHelper.PostToMainThread(new SimpleMainTask(() =>
            {
                MainThread.Instance.TimerWheel.AddTimerOnce(5000, (_, __, ___) =>
                {
                    if (DoingJoinDynamicBattleTasks.TryRemove(seq, out var tcs))
                    {
                        tcs.SetException(new RpcException(new Status(StatusCode.Cancelled, "time out.")));
                    }
                });
            }));
            return taskSrc.Task;
        }

        public void OnCreateTeamResponse(long seq, CreateTeamRsp rsp)
        {
            if (DoingCreateTeamTasks.TryRemove(seq, out var tcs))
            {
                logger.Info($"CreateTeamResponse: {seq} {rsp.Team?.BattleServerID} {rsp.Team?.TeamID} {rsp.Team?.LeaderID}");
                tcs.SetResult(rsp);
            }
            else
            {
                logger.Warn($"OnCreateTeamResponse {seq}, but request not found");
            }
        }

        public void OnKickOutResponse(long seq, EOpCode code)
        {
            if (DoingKickOutTasks.TryRemove(seq, out var tcs))
            {
                logger.Info($"OnKickOutResponse {seq} code {code}");
                if (code == EOpCode.Success || code == EOpCode.NotSendEnterButKicked)
                {
                    tcs.SetResult(new Empty());
                }
                else if (code == EOpCode.SendLeaveError)
                {
                    tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "kick out failed due to send leave error")));
                }
                else
                {
                    tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "kick out failed")));
                }
            }
            else
            {
                logger.Warn($"OnKickOutResponse {seq}, but request not found");
            }
        }

        public override Task<ReplyTeamInvitationRsp> ReplyTeamInvitation(ReplyTeamInvitationReq request, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"ReplyTeamInvitation from lobby but server not ready {request.ReceiverID} {request.SenderID}");
                return Task.FromException<ReplyTeamInvitationRsp>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"ReplyTeamInvitation {request.ReceiverID} {request.SenderID}");
            var seq = currentSeq++;
            var taskSrc = new TaskCompletionSource<ReplyTeamInvitationRsp>();
            DoingReplyTeamInvitationTasks.TryAdd(seq, taskSrc);
            ThreadHelper.PostToMainThread(new SimpleMainTask(() =>
            {
                MainThread.Instance.TimerWheel.AddTimerOnce(5000, (_, __, ___) =>
                {
                    if (DoingReplyTeamInvitationTasks.TryRemove(seq, out var tcs))
                    {
                        tcs.SetResult(new ReplyTeamInvitationRsp { Success = false, MsgID = -1 });
                    }
                });
            }));

            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_REPLY_TEAM_INVITATION_FROM_LOBBY, ERpcTarget.World);
            packet.AppendParam(seq).AppendParam(request.ToByteArray());
            WorldNetworkHelper.GateCallWorld(packet);
            return taskSrc.Task;
        }

        public void OnReplyTeamInvitationResponse(long seq, byte[] rspBytes)
        {
            var rsp = ReplyTeamInvitationRsp.Parser.ParseFrom(rspBytes);
            OnReplyTeamInvitationResponse(seq, rsp);
        }

        public void OnReplyTeamInvitationResponse(long seq, ReplyTeamInvitationRsp rsp)
        {
            if (DoingReplyTeamInvitationTasks.TryRemove(seq, out var tcs))
            {
                logger.Info($"OnReplyTeamInvitationResponse {seq} {rsp.MsgID} {rsp.Name}");
                tcs.SetResult(rsp);
            }
            else
            {
                logger.Warn($"OnReplyTeamInvitationResponse {seq}, but request not found");
            }
        }

        public override Task<Empty> PlayerDeleteBattle(PlayerDeleteBattleReq request, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"ReplyTeamInvitation from lobby but server not ready {request.RoleID}");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"PlayerDeleteBattle {request.RoleID}");
            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_PLAYER_DELETE_BATTLE, ERpcTarget.World);
            packet.AppendParam(request.RoleID);
            WorldNetworkHelper.GateCallWorld(packet);
            return Task.FromResult(new Empty());
        }

        public override Task<Empty> HealthCheck(Empty request, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"HealthCheck from lobby but server not ready");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }
            return Task.FromResult(new Empty());
        }

        public override Task<Empty> NotifyRoleResourceChange(ResourceNotifyReq req, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"NotifyRoleResourceChange from lobby but server not ready");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"NotifyRoleResourceChange {req.RoleID} {req.Resources.ToString()}");
            if (req.Resources.Count == 0)
            {
                logger.Warn($"NotifyRoleResourceChange {req.RoleID} empty resource");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.InvalidArgument, "no resources")));
            }

            var reqBytes = req.ToByteArray();
            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_NOTIFY_ROLE_RESOURCE_CHENGE, ERpcTarget.World);
            packet.AppendParam(reqBytes);
            WorldNetworkHelper.GateCallWorld(packet);
            return Task.FromResult(new Empty());
        }

        public override Task<ApplyJoinBattleRecruitmentRsp> ApplyJoinBattleRecruitment(ApplyJoinBattleRecruitmentReq req, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"ApplyJoinBattleRecruitment from lobby but server not ready");
                return Task.FromException<ApplyJoinBattleRecruitmentRsp>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"ApplyJoinBattleRecruitment {req.TeamID} {req.ApplicantID}");
            var reqBytes = req.ToByteArray();

            var seq = currentSeq++;
            var taskSrc = new TaskCompletionSource<ApplyJoinBattleRecruitmentRsp>();
            DoingJoinBattleRecruitmentTasks.TryAdd(seq, taskSrc);
            ThreadHelper.PostToMainThread(new SimpleMainTask(() =>
            {
                MainThread.Instance.TimerWheel.AddTimerOnce(5000, (_, __, ___) =>
                {
                    if (DoingJoinBattleRecruitmentTasks.TryRemove(seq, out var tcs))
                    {
                        tcs.SetResult(new ApplyJoinBattleRecruitmentRsp { Success = false, MsgID = -1 });
                    }
                });
            }));

            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_APPLY_JOIN_BATTLE_RECRUITMENT, ERpcTarget.World);
            packet.AppendParam(seq).AppendParam(reqBytes);
            WorldNetworkHelper.GateCallWorld(packet);
            return taskSrc.Task;
        }

        public void OnApplyJoinBattleRecruitment(long seq, ApplyJoinBattleRecruitmentRsp rsp)
        {
            if (DoingJoinBattleRecruitmentTasks.TryRemove(seq, out var tcs))
            {
                logger.Info($"OnApplyJoinBattleRecruitment response {seq}");
                tcs.SetResult(rsp);

            }
            else
            {
                logger.Warn($"OnApplyJoinBattleRecruitment {seq}, but request not found");
            }
        }

        public void OnPushChangeTalentsAck(long seq, EOpCode code)
        {
            if (DoingPushChangeTalentsTasks.TryRemove(seq, out var tcs))
            {
                logger.Info($"OnPushChangeTalentsAck response {seq} code {code}");
                if (code != EOpCode.Success)
                {
                    tcs.SetException(new RpcException(new Status(StatusCode.Aborted, $"push change talents failed with code {code}")));
                }
                else
                {
                    tcs.SetResult(new Empty());
                }
            }
            else
            {
                logger.Warn($"OnPushChangeTalentsAck {seq}, but request not found");
            }
        }

        public void OnJoinDynamicBattleResponse(long seq, EOpCode code)
        {
            if (DoingJoinDynamicBattleTasks.TryRemove(seq, out var tcs))
            {
                logger.Info($"OnJoinDynamicBattleResponse {seq} code {code}");
                switch (code)
                {
                    case EOpCode.Success:
                        {
                            tcs.SetResult(new Empty());
                        }
                        break;
                    case EOpCode.SupplyIdMismatch:
                        {
                            tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "join dynamic battle failed, supply id mismatch.")));
                        }
                        break;
                    case EOpCode.NotBombHome:
                        {
                            tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "join dynamic battle failed, not bomb home.")));
                        }
                        break;
                    case EOpCode.RepeatJoin:
                        {
                            tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "join dynamic battle failed, repeat join.")));
                        }
                        break;
                    case EOpCode.NoSupplyReq:
                        {
                            tcs.SetException(new RpcException(new Status(StatusCode.Aborted, "join dynamic battle failed, no supply request.")));
                        }
                        break;
                    default:
                        {
                            tcs.SetException(new RpcException(new Status(StatusCode.Aborted, $"join dynamic battle failed with code {code}")));
                        }
                        break;
                }
            }
            else
            {
                logger.Warn($"OnJoinDynamicBattleResponse {seq}, but request not found");
            }
        }

        public override Task<Empty> PushChangeTalents(PushChangeTalentsReq req, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"ApplyJoinBattleRecruitment from lobby but server not ready");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"PushChangeTalents {req.RoleID} {req.ToString()}");
            var reqBytes = req.ToByteArray();

            var seq = currentSeq++;
            var taskSrc = new TaskCompletionSource<Empty>();
            DoingPushChangeTalentsTasks.TryAdd(seq, taskSrc);
            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_PUSH_CHANGE_TALENTS, ERpcTarget.World);
            packet.AppendParam(seq).AppendParam(reqBytes);
            WorldNetworkHelper.GateCallWorld(packet);
            return taskSrc.Task;
        }

        public override Task<Empty> PushNewTaskGroups(PushNewTaskGroupsReq req, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"[PushNewTaskGroups] from lobby but server not ready");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"[PushNewTaskGroups] {req.RoleID} ");
            if (req.DSNewTaskGroups.Count == 0)
            {
                logger.Warn($"[PushNewTaskGroups] {req.RoleID} DSNewTaskGroups empty");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.InvalidArgument, "no DSNewTaskGroups")));
            }

            var reqBytes = req.ToByteArray();
            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_PUSH_NEW_TASK_GROUPS, ERpcTarget.World);
            packet.AppendParam(reqBytes);
            WorldNetworkHelper.GateCallWorld(packet);
            return Task.FromResult(new Empty());
        }

        public override Task<Empty> GMTaskFinish(GMTaskFinishReq req, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"[GMTaskFinish] from lobby but server not ready");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"[GMTaskFinish] Processing GM task finish request for role {req.RoleID}, taskGroups count: {req.TaskGroups.Count}");

            if (req.TaskGroups.Count == 0)
            {
                logger.Warn($"[GMTaskFinish] {req.RoleID} TaskGroups empty");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.InvalidArgument, "no TaskGroups")));
            }

            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_GM_TASK_FINISH, ERpcTarget.World);
            packet.AppendParam(req.ToByteArray());
            WorldNetworkHelper.GateCallWorld(packet);

            return Task.FromResult(new Empty());
        }

        public override Task<Empty> PushStyleRankChange(PushStyleRankChangeReq req, ServerCallContext context)
        {
            if (!MainThread.Instance.IsAllReady)
            {
                logger.Warn($"[PushStyleRankChange] from lobby but server not ready");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.Unavailable, "server not ready")));
            }

            logger.Info($"[PushStyleRankChange] Processing style rank change request for role {req.RoleID}, styleRankUpdates count: {req.StyleRankUpdates.Count}");

            if (req.StyleRankUpdates.Count == 0)
            {
                logger.Warn($"[PushStyleRankChange] {req.RoleID} StyleRankUpdates empty");
                return Task.FromException<Empty>(new RpcException(new Status(StatusCode.InvalidArgument, "no StyleRankUpdates")));
            }

            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_PUSH_STYLE_RANK_CHANGE, ERpcTarget.World);
            packet.AppendParam(req.ToByteArray());
            WorldNetworkHelper.GateCallWorld(packet);

            return Task.FromResult(new Empty());
        }
    }
}
